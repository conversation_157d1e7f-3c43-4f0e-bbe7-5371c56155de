#!/usr/bin/env python3
"""
Test script to verify the server can handle multiple concurrent connections
without getting stuck.
"""

import asyncio
import websockets
import json
import time
import sys
from concurrent.futures import ThreadPoolExecutor
import requests

# Configuration
SERVER_URL = "ws://localhost:8000/agent/chat"
HEALTH_URL = "http://localhost:8000/agent/health"
NUM_CONCURRENT_CONNECTIONS = 10
TEST_MESSAGE = "Hello, can you help me find a job?"

async def test_websocket_connection(connection_id: int):
    """Test a single WebSocket connection"""
    try:
        print(f"Connection {connection_id}: Starting...")
        
        async with websockets.connect(SERVER_URL) as websocket:
            print(f"Connection {connection_id}: Connected")
            
            # Send a test message
            message = {
                "prev_page": "",
                "current_page": "chat",
                "position_id": "",
                "message": f"{TEST_MESSAGE} (Connection {connection_id})"
            }
            
            await websocket.send(json.dumps(message))
            print(f"Connection {connection_id}: Message sent")
            
            # Wait for response
            response_count = 0
            start_time = time.time()
            
            while response_count < 5 and (time.time() - start_time) < 30:  # 30 second timeout
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    data = json.loads(response)
                    response_count += 1
                    
                    print(f"Connection {connection_id}: Response {response_count} - Type: {data.get('message_type', 'unknown')}")
                    
                    if data.get('message_type') == 'end':
                        print(f"Connection {connection_id}: Conversation ended")
                        break
                        
                except asyncio.TimeoutError:
                    print(f"Connection {connection_id}: Timeout waiting for response")
                    break
                except json.JSONDecodeError as e:
                    print(f"Connection {connection_id}: JSON decode error: {e}")
                    break
            
            print(f"Connection {connection_id}: Completed successfully")
            return True
            
    except Exception as e:
        print(f"Connection {connection_id}: Error - {e}")
        return False

async def test_concurrent_connections():
    """Test multiple concurrent WebSocket connections"""
    print(f"Testing {NUM_CONCURRENT_CONNECTIONS} concurrent connections...")
    
    # First, check if server is healthy
    try:
        response = requests.get(HEALTH_URL, timeout=5)
        if response.status_code != 200:
            print(f"Server health check failed: {response.status_code}")
            return False
        print("Server health check passed")
    except Exception as e:
        print(f"Server health check failed: {e}")
        return False
    
    # Create concurrent connections
    start_time = time.time()
    
    tasks = []
    for i in range(NUM_CONCURRENT_CONNECTIONS):
        task = asyncio.create_task(test_websocket_connection(i + 1))
        tasks.append(task)
    
    # Wait for all connections to complete
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    end_time = time.time()
    duration = end_time - start_time
    
    # Analyze results
    successful_connections = sum(1 for result in results if result is True)
    failed_connections = len(results) - successful_connections
    
    print(f"\n=== Test Results ===")
    print(f"Total connections: {NUM_CONCURRENT_CONNECTIONS}")
    print(f"Successful connections: {successful_connections}")
    print(f"Failed connections: {failed_connections}")
    print(f"Total duration: {duration:.2f} seconds")
    print(f"Average time per connection: {duration/NUM_CONCURRENT_CONNECTIONS:.2f} seconds")
    
    if failed_connections > 0:
        print("\nErrors encountered:")
        for i, result in enumerate(results):
            if result is not True:
                print(f"  Connection {i+1}: {result}")
    
    return failed_connections == 0

def test_http_endpoints():
    """Test HTTP endpoints under load"""
    print("\nTesting HTTP endpoints...")
    
    def test_health_endpoint():
        try:
            response = requests.get(HEALTH_URL, timeout=5)
            return response.status_code == 200
        except:
            return False
    
    # Test concurrent HTTP requests
    with ThreadPoolExecutor(max_workers=20) as executor:
        futures = [executor.submit(test_health_endpoint) for _ in range(50)]
        results = [future.result() for future in futures]
    
    successful_requests = sum(results)
    print(f"HTTP test: {successful_requests}/50 requests successful")
    
    return successful_requests >= 45  # Allow some failures

async def main():
    """Main test function"""
    print("=== Concurrent Connection Test ===")
    print("This test verifies that the server can handle multiple")
    print("concurrent connections without getting stuck.\n")
    
    # Test WebSocket connections
    websocket_success = await test_concurrent_connections()
    
    # Test HTTP endpoints
    http_success = test_http_endpoints()
    
    print(f"\n=== Final Results ===")
    print(f"WebSocket test: {'PASSED' if websocket_success else 'FAILED'}")
    print(f"HTTP test: {'PASSED' if http_success else 'FAILED'}")
    
    overall_success = websocket_success and http_success
    print(f"Overall test: {'PASSED' if overall_success else 'FAILED'}")
    
    if overall_success:
        print("\n✅ Server successfully handles concurrent connections!")
    else:
        print("\n❌ Server has issues with concurrent connections.")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        sys.exit(1)
