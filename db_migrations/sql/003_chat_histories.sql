CREATE TABLE IF NOT EXISTS public.chat_histories
(
    id bigserial NOT NULL,
    session_id character varying(255) NOT NULL,
    active_agent character varying(255) NOT NULL,
    message_id character varying(255),
    role character varying(255) NOT NULL,
    content text,
    tool_call_id character varying(255),
    tool_name character varying(255),
    tool_input json,
    created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at timestamp without time zone,
    CONSTRAINT chat_histories_pkey PRIMARY KEY (id)
)
