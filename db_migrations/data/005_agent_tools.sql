--
-- PostgreSQL database dump
--

-- Dumped from database version 16.4 (Debian 16.4-1.pgdg120+2)
-- Dumped by pg_dump version 17.0

-- Started on 2025-06-13 00:21:53 JST

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
-- SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 3645 (class 0 OID 79077)
-- Dependencies: 238
-- Data for Name: agent_tools; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.agent_tools (id, agent_id, tool_name, created_at, updated_at, deleted_at) VALUES (1, 2, 'save_salary_preference', '2025-04-16 05:16:39.331359', '2025-06-12 15:21:18.613985', NULL);
INSERT INTO public.agent_tools (id, agent_id, tool_name, created_at, updated_at, deleted_at) VALUES (2, 2, 'search_job_postings', '2025-04-16 05:16:39.331359', '2025-06-11 12:45:21.611734', NULL);
INSERT INTO public.agent_tools (id, agent_id, tool_name, created_at, updated_at, deleted_at) VALUES (3, 2, 'search_industries_by_sentence', '2025-06-12 15:21:18.613985', NULL, NULL);
INSERT INTO public.agent_tools (id, agent_id, tool_name, created_at, updated_at, deleted_at) VALUES (4, 2, 'search_occupations_by_sentence', '2025-06-12 15:21:18.613985', NULL, NULL);
INSERT INTO public.agent_tools (id, agent_id, tool_name, created_at, updated_at, deleted_at) VALUES (5, 2, 'search_occupations_by_work_nature', '2025-06-12 15:21:18.613985', NULL, NULL);
INSERT INTO public.agent_tools (id, agent_id, tool_name, created_at, updated_at, deleted_at) VALUES (6, 2, 'save_industry_preference', '2025-06-12 15:21:18.613985', NULL, NULL);
INSERT INTO public.agent_tools (id, agent_id, tool_name, created_at, updated_at, deleted_at) VALUES (8, 2, 'save_occupation_preference', '2025-06-12 15:21:18.613985', NULL, NULL);
INSERT INTO public.agent_tools (id, agent_id, tool_name, created_at, updated_at, deleted_at) VALUES (9, 2, 'save_current_residence_location', '2025-06-12 15:21:18.613985', NULL, NULL);


--
-- TOC entry 3651 (class 0 OID 0)
-- Dependencies: 237
-- Name: agent_tools_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.agent_tools_id_seq', 9, true);


-- Completed on 2025-06-13 00:21:53 JST

--
-- PostgreSQL database dump complete
--

