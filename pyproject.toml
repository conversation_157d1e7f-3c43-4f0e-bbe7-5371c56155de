[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "AICA-Agent"
version = "0.0.1"
description = "エージェントサーバー"
readme = "README.md"
requires-python = ">=3.13,<3.14"
classifiers = [
    "Programming Language :: Python :: 3.13",
    "Operating System :: OS Independent",
]
dependencies = [
    "uvicorn==0.34.0",
    "gunicorn==23.0.0",
    "fastapi[standard]==0.115.12",
    "psycopg[binary]==3.2.6",
    "SQLAlchemy==2.0.40",
    "dependency-injector==4.46.0",
    "backoff==2.2.1",
    "openai-agents[litellm]==0.0.16",
    "boto3==1.38.14",
    "mcp==1.9.3"
]

[project.urls]
Repository = "https://github.com/MIIDAS-Company/miidas_aica_agent"
Issues = "https://github.com/MIIDAS-Company/miidas_aica_agent/issues"

[tool.hatch.build.targets.wheel]
packages = ["aica_agent"]

[tool.hatch.metadata]
allow-direct-references = true
