import threading
from typing import <PERSON>V<PERSON>, Generic, Dict

_KT = TypeVar("_KT")
_VT = TypeVar("_VT")

class SynchronizedDict(Generic[_KT, _VT]):
    def __init__(self):
        self._dict: Dict[_KT, _VT] = dict()
        self._lock = threading.Lock()

    def get(self, key: _KT) -> _VT:
        with self._lock:
            if key not in self._dict:
                return None
            value = self._dict[key]
            return value

    def set(self, key: _KT, value: _VT) -> None:
        with self._lock:
            self._dict[key] = value

    def pop(self, key: _KT, default=None) -> _VT:
        with self._lock:
            if key not in self._dict:
                return None
            value = self._dict.pop(key, default)
            return value

    def items(self):
        with self._lock:
            items = list(self._dict.items())
            return items

    def keys(self):
        with self._lock:
            keys = list(self._dict.keys())
            return keys

    def values(self):
        with self._lock:
            values = list(self._dict.values())
            return values

    def __contains__(self, key: _KT) -> bool:
        with self._lock:
            exists = key in self._dict
            return exists

    def setdefault(self, key: _KT, default: _VT) -> _VT:
        with self._lock:
            return self._dict.setdefault(key, default)
