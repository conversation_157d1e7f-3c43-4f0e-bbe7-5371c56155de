import logging.config

from dependency_injector import containers, providers

from database import Database
from repositories.llm_repo import LLMRepository
from repositories import agent_repo, chat_repo, mcp_repo, position_repo
from services import chat_service, position_service
from utils.logging import record_factory


class Container(containers.DeclarativeContainer):
    """
    Dependency Injecttion
    """

    wiring_config = containers.WiringConfiguration(modules=["endpoints"])

    config = providers.Configuration(yaml_files=["config.yml"])

    logging.setLogRecordFactory(record_factory)
    logging = providers.Resource(
        logging.config.dictConfig,
        config=config.logging,
    )

    db = providers.Singleton(Database, db_url=config.db.url)

    agent_repository = providers.Factory(
        agent_repo.AgentRepository,
        session_factory=db.provided.session,
    )

    mcp_repository = providers.Factory(
        mcp_repo.McpRepository,
        session_factory=db.provided.session,
    )
    chat_repository = providers.Factory(
        chat_repo.ChatRepository,
        session_factory=db.provided.session,
    )

    llm_repository = providers.Resource(
        LLMRepository,
        mcp_url=config.mcp.url,
        model_list=config.model_list,
        agent_repository=agent_repository,
        mcp_repository=mcp_repository,
    )

    # TODO: 本当はFactoryにしたかったのですが、実際のデータを持っているので、Singletonにしました。
    # 今後もしRedisなど外部メモリを利用する場合、他と同じくFactoryにします。
    position_repository = providers.Singleton(
        position_repo.PositionRepository,
    )

    chat_service = providers.Factory(
        chat_service.ChatService,
        llm_repository=llm_repository,
        chat_repository=chat_repository,
        position_repository=position_repository,
    )

    position_service = providers.Factory(
        position_service.PositionService,
        position_repository=position_repository,
        api_url=config.api.url,
    )
