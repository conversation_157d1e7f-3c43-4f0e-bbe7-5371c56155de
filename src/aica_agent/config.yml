logging:
  version: 1
  disable_existing_loggers: False
  formatters:
    formatter:
      format: "[%(asctime)s] [%(levelname)s] [%(name)s] [%(session_id)s]: %(message)s"
  handlers:
    console:
      class: "logging.StreamHandler"
      level: "DEBUG"
      formatter: "formatter"
      stream: "ext://sys.stdout"
    file:
      class: "logging.FileHandler"
      level: "DEBUG"
      formatter: "formatter"
      filename: "/tmp/aica_agent.log"
      mode: "a"
  root:
    level: "DEBUG"
    handlers:
      - console
      - file
  loggers:
    gunicorn.error:
      handlers:
        - console
        - file
      propagate: False
    gunicorn.access:
      handlers:
        - console
        - file
      propagate: False
    LiteLLM:
      level: "INFO"
      handlers:
        - console
        - file
      propagate: False
db:
  url: "postgresql+psycopg://${AICA_AGENT_DB_USER}:${AICA_AGENT_DB_PASSWORD}@${AICA_AGENT_DB_HOST}:${AICA_AGENT_DB_PORT}/${AICA_AGENT_DB_NAME}?sslmode=${AICA_AGENT_DB_SSLMODE}"
model_list:
  - model_name: bedrock-claude-v1
    model_settings:
      model: bedrock/anthropic.claude-3-5-sonnet-20240620-1:0
      temperature: 1.0
      top_p: 0.999
      aws_access_key_id: os.environ/AWS_ACCESS_KEY_ID
      aws_secret_access_key: os.environ/AWS_SECRET_ACCESS_KEY
      aws_session_token: os.environ/AWS_SESSION_TOKEN
      aws_region_name: os.environ/AWS_REGION_NAME
  - model_name: openai-gpt-4.1
    model_settings:
      model: openai/gpt-4.1
      temperature: 1.0
      top_p: 0.999
mcp:
  url: "${AICA_AGENT_MCP_ENDPOINT}"
api:
  url: "${AICA_AGENT_API_ENDPOINT}"
