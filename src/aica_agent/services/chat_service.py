import copy
from enum import Str<PERSON><PERSON>
import json
from typing import AsyncGenerator
import uuid
from typing import <PERSON>ple
from datetime import datetime

from openai.types.responses import ResponseTextDeltaEvent, ResponseOutputTextParam
from openai.types.responses.response_input_item_param import Message
from openai.types.responses.response_input_text_param import ResponseInputTextParam
from agents import (
    Agent,
    HandoffCallItem,
    HandoffOutputItem,
    MessageOutputItem,
    ModelSettings,
    ReasoningItem,
    RunConfig,
    RunItem,
    Runner,
    TResponseInputItem,
    ToolCallItem,
    ToolCallOutputItem,
    RunResultStreaming,
)

from domain.enitites.chat_history import ChatHistory
from repositories.llm_repo import AgentName, LLMRepository
from repositories.position_repo import PositionRepository
from repositories.chat_repo import ChatRepository
from services.base_service import BaseService
from utils.chat_response import ChatStreamReponse

DEFAULT_ERROR_MESSAGE = (
    "大変混み合っておりますので、しばらく経ってからリロードしてご利用ください。"
)
POSITION_SEARCH_FAKE_RESULT = "ポジション検索が実行されました。ユーザーには別の手段で求人の検索結果を見せていますが、ユーザーから再度検索の要望があれば、検索条件の差異に関わらず、再度このツールを実行してください。"


class LLMMessageRole(StrEnum):
    DEVELOPER = "developer"
    USER = "user"
    ASSISTANT = "assistant"
    TOOL = "tool"
    HANDOFF = "handoff"
    REASONING = "reasoning"
    SYSTEM = "system"


class MessageType(StrEnum):
    MESSAGE = "message"
    POSITION_SEARCH_RESULT = "position_search_result"
    ERROR = "error"
    END = "end"


class PageName(StrEnum):
    CHAT = "Chat"
    POSITION_DETAIL = "PositionDetail"


class ChatService(BaseService):
    """
    LLM会話サービス
    """

    def __init__(
        self,
        llm_repository: LLMRepository,
        chat_repository: ChatRepository,
        position_repository: PositionRepository,
    ) -> None:
        """
        インスタンス初期化

        Args:
            llm_repository: LLMモデルリポジトリ
            chat_repository: 会話履歴リポジトリ
            position_repository: ポジションデータリポジトリ
        """
        super().__init__()

        # LLMプロバイダー（OpenAI、Bedrock）
        self._provider: str | None = None
        self._llm_repository = llm_repository
        self._chat_repository = chat_repository
        self._position_repository = position_repository
        # 今会話しているエージェント
        self._active_agent: Agent = None
        # セッション作成済みフラグ。※挨拶だけの会話は保存しないので、このフラグを設けています。
        # True：chat_sessionデータがすでに作られている
        # False：新しいセッションなので、まだchat_sessionデータが作られていない。
        self._session_created = False
        # DB保存フラグ。※挨拶だけの会話は保存しないので、このフラグを設けています。
        # LLMとの会話は1回だけの場合、挨拶扱いとしてDBデータは保存しない
        # 2回目から、初めての挨拶を含めてDBにデータを保存します。
        # True：LLMとの会話は2回以上の場合、保存すべき
        # False：まだ挨拶だけなので、DB保存はしない
        self._should_save = False
        # 会話履歴
        self._conversation_history = []

    async def init_session(
        self,
        session_id: str,
        provider: str,
    ) -> bool:
        """
        セッション初期化。※セッションはwebsocket接続毎に１つ

        Args:
            session_id: セッションID
            provider: LLMプロバイダー
        """
        self._session_id = session_id
        self._provider = provider

        active_agent_name = None
        try:
            chat_session = self._chat_repository.init_chat_session(
                self._session_id,
            )
            if chat_session:
                # DBにセッションデータがあるので、セッション作成済みフラグをTrueにする。
                self._session_created = True
                if chat_session.histories:
                    # 既存セッションの再開なので、前回のアクティブエージェントを続いて利用する。
                    active_agent_name = chat_session.histories[-1].active_agent
                    # 以前の会話から続くため、会話履歴をロードして、LLMに渡す。
                    self._conversation_history.extend(
                        self._convert_to_llm_messages(chat_session.histories)
                    )
                    # DB保存済みセッションの再開なので、つまりLLMと2回以上会話しているので、DB保存フラグをTrueにする。
                    self._should_save = True

            if not self._conversation_history:
                self._conversation_history.append(
                    {
                        "type": "message",
                        "role": LLMMessageRole.DEVELOPER,
                        "content": f"""### ツール呼び出すときのパラメータについて
        セッションID：{session_id}を利用してください。
        リクエストID：なるべく重複しないよう任意のuuidを生成して、リクエストごとにユニークな値を設定してください。
                        """,
                    }
                )


            self._active_agent = self._llm_repository.active_agent(
                self._provider,
                active_agent_name,
            )

            return True
        except Exception as e:
            self.logger.error(f"Error initializing session: {e}", exc_info=True)
            return False

    def _convert_to_llm_messages(
        self,
        histories: list[ChatHistory],
    ) -> list:
        """
        DB履歴でデータからLLM会話履歴作成。

        Args:
            histories: DB履歴でデータ
        """
        messages = []
        for history in histories:
            if history.role in [LLMMessageRole.USER, LLMMessageRole.DEVELOPER]:
                messages.append(
                    {
                        "type": "message",
                        "role": history.role,
                        "content": history.content,
                    }
                )
            elif history.role == LLMMessageRole.ASSISTANT:
                messages.append(
                    {
                        "type": "message",
                        "role": history.role,
                        "content": [
                            ResponseOutputTextParam(
                                type="output_text",
                                text=history.content,
                            )
                        ],
                    }
                )
            elif history.role in [LLMMessageRole.TOOL, LLMMessageRole.HANDOFF]:
                messages.append(
                    {
                        "type": "function_call",
                        "call_id": history.tool_call_id,
                        "name": history.tool_name,
                        "arguments": json.dumps(history.tool_input),
                    }
                )

                output = history.content
                agent = self._llm_repository.active_agent(
                    self._provider, history.active_agent
                )
                if isinstance(agent.tool_use_behavior, dict):
                    names = agent.tool_use_behavior.get("stop_at_tool_names", [])
                    if history.tool_name in names:
                        # return_redirect=Trueのツールの場合、実際の実行結果ではなく、フェイク結果をLLMに渡す
                        output = POSITION_SEARCH_FAKE_RESULT

                messages.append(
                    {
                        "type": "function_call_output",
                        "call_id": history.tool_call_id,
                        "output": output,
                    }
                )
            elif history.role == LLMMessageRole.REASONING:
                pass
            else:
                self.logger.error(f"Unsupported message role: {history}")
        return messages

    # TODO: ちょっと長いので、分割できる？
    async def chat(
        self,
        input: dict,
    ) -> AsyncGenerator[str, None]:
        """
        ユーザーインプットをLLMに渡して、レスポンスを返す。
        インプットごとに呼び出される

        Args:
            message: ユーザーインプット

        Returns:
            LLMレスポンス
        """
        self.logger.debug(f"Chat: {input}")

        prev_page = input["prev_page"]
        current_page = input["current_page"]
        position_uuid = input["position_id"]
        message = input["message"]

        role = LLMMessageRole.USER

        # tell which agent should be the active agent
        if current_page == PageName.CHAT:
            # `/chat` first access or reload
            # OR
            # `/positions/{position_id}` => `/chat`
            # if prev_page == PageName.POSITION_DETAIL:
            # `/positions/{position_id}` => `/chat`
            # 一旦LLMに詳細お問い合わせ後メッセージ送信をやめました
            # if message == "###PositionAdvisorEnd###":
            #     role = LLMMessageRole.DEVELOPER
            #     message = "指定ポジションに対してのお問い合わせ完了"
            # else:
            #     self.logger.error(f"Unsupported message: {message}")
            #     yield ChatStreamReponse(
            #         self._session_id,
            #         position_uuid,
            #     ).create_error_response(
            #         f"Unsupported message",
            #     )

            if self._active_agent.name == AgentName.POSITION_GUIDE:
                self._active_agent = self._llm_repository.active_agent(
                    self._provider,
                    AgentName.CAREER_ADVISOR,
                )
        elif current_page == PageName.POSITION_DETAIL and position_uuid:
            # `/chat` => `/positions/{position_id}`
            # or
            # `/positions/{position_id}` reload
            if message == "###PositionAdvisorStart###":
                position_detail, company_detail, business_detail, error_message = (
                    self._get_position_detail(position_uuid)
                )

                if error_message:
                    self.logger.error(error_message)
                    yield ChatStreamReponse(
                        self._session_id,
                        position_uuid,
                    ).create_error_response(
                        error_message,
                    )
                    return

                role = LLMMessageRole.DEVELOPER
                message = "指定ポジションは下記となります。\n\n%s\n%s\n%s" % (
                    json.dumps(position_detail),
                    json.dumps(company_detail),
                    json.dumps(business_detail),
                )

            if self._active_agent.name != AgentName.POSITION_GUIDE:
                self._active_agent = self._llm_repository.active_agent(
                    self._provider,
                    AgentName.POSITION_GUIDE,
                )
        else:
            self.logger.error(f"Unsupported page: {current_page}")
            yield ChatStreamReponse(
                self._session_id,
                position_uuid,
            ).create_error_response(
                f"Unsupported page",
            )

        self._conversation_history.append(
            Message(
                type="message",
                role=role,
                content=[ResponseInputTextParam(type="input_text", text=message)],
            ),
        )

        try:
            while True:
                position_search_validation_error = False
                stop_at_tool_calls: dict[str, ChatHistory] = {}

                chat_response = ChatStreamReponse(
                    self._session_id,
                    position_uuid,
                    str(uuid.uuid4()),
                )
                result = Runner.run_streamed(
                    self._active_agent,
                    input=self._conversation_history,
                )
                user_preferences_save_tool_calls: list[str] = []
                async for event in result.stream_events():
                    if event.type == "raw_response_event":
                        if isinstance(event.data, ResponseTextDeltaEvent):
                            yield chat_response.create_message_response(
                                event.data.delta
                            )
                    elif event.type == "run_item_stream_event":
                        if event.item.type == "tool_call_item":
                            # tool_call_output_itemからツール名が取れないので、ここは検索条件保存ツールのcall_idを保存
                            if event.item.raw_item.name.startswith(
                                "save_"
                            ) and event.item.raw_item.name.endswith("_preference"):
                                user_preferences_save_tool_calls.append(
                                    event.item.raw_item.call_id
                                )
                                continue

                            agent = event.item.agent
                            if isinstance(agent.tool_use_behavior, dict):
                                # 現在return_directツールはポジション検索しかないので、その前提で下記の処理を行う
                                names = agent.tool_use_behavior.get(
                                    "stop_at_tool_names", []
                                )
                                if event.item.raw_item.name in names:
                                    stop_at_tool_calls[event.item.raw_item.call_id] = (
                                        ChatHistory(
                                            session_id=self._session_id,
                                            active_agent=agent.name,
                                            message_id=event.item.raw_item.id,
                                            role=LLMMessageRole.TOOL,
                                            tool_call_id=event.item.raw_item.call_id,
                                            tool_name=event.item.raw_item.name,
                                            tool_input=json.loads(
                                                event.item.raw_item.arguments
                                            ),
                                        )
                                    )

                                    # ポジション検索条件保存
                                    try:
                                        tool_input = json.loads(
                                            event.item.raw_item.arguments
                                        )
                                        tool_input.pop("SessionID", None)
                                        tool_input.pop("RequestID", None)
                                        self._position_repository.save_user_preference(
                                            self._session_id,
                                            tool_input,
                                        )
                                    except (
                                        json.JSONDecodeError,
                                        AttributeError,
                                        TypeError,
                                    ) as e:
                                        self.logger.error(
                                            f"ポジション検索条件保存失敗しました: {e}",
                                            exc_info=True,
                                        )
                        elif event.item.type == "tool_call_output_item":
                            # ユーザー検索条件保存
                            if (
                                event.item.raw_item["call_id"]
                                in user_preferences_save_tool_calls
                            ):
                                try:
                                    tool_result = json.loads(
                                        event.item.raw_item["output"]
                                    )
                                    tool_result = json.loads(tool_result["text"])
                                    if "Message" in tool_result:
                                        self.logger.error(
                                            f"Failed to save user preference: {tool_result['error']}",
                                            exc_info=True,
                                        )
                                    else:
                                        self._position_repository.save_user_preference(
                                            self._session_id,
                                            tool_result,
                                        )
                                except (json.JSONDecodeError, TypeError, KeyError) as e:
                                    self.logger.error(
                                        f"Failed to parse user preference JSON: {e}",
                                        exc_info=True,
                                    )

                            if event.item.raw_item["call_id"] in stop_at_tool_calls:
                                # 現在return_directツールはポジション検索しかないので、その前提で下記の処理を行う
                                stop_at_tool_calls[
                                    event.item.raw_item["call_id"]
                                ].content = event.item.raw_item["output"]

                                try:
                                    tool_result = json.loads(
                                        event.item.raw_item["output"]
                                    )
                                    tool_result = json.loads(tool_result["text"])
                                    if "Message" in tool_result:
                                        # ポジション検索バリデーションエラーなので、エラーメッセージをLLMに渡して対応してもらう
                                        position_search_validation_error = True
                                        continue

                                    current_datetime = datetime.now()
                                    timestamp = current_datetime.timestamp()
                                    search_key = str(int(timestamp))

                                    position_summaries = tool_result["Positions"]

                                    position_id_2_uuid = self._position_repository.save_position_summaries(
                                        self._session_id,
                                        copy.deepcopy(position_summaries),
                                    )

                                    for position in position_summaries:
                                        if "ID" in position:
                                            position["ID"] = position_id_2_uuid[
                                                position["ID"]
                                            ]
                                        if "CompanyID" in position:
                                            position.pop("CompanyID", None)
                                        if "BusinessID" in position:
                                            position.pop("BusinessID", None)

                                    if "Recommendations" in tool_result:
                                        recommendations = tool_result["Recommendations"]
                                        theme_mapping = self._position_repository.save_position_recommendations(
                                            self._session_id,
                                            [
                                                recommendation["Theme"]
                                                for recommendation in recommendations
                                            ],
                                        )

                                        for recommendation in recommendations:
                                            if recommendation["Theme"] in theme_mapping:
                                                recommendation["Theme"] = theme_mapping[
                                                    recommendation["Theme"]
                                                ]
                                    else:
                                        recommendations = []

                                    yield ChatStreamReponse(
                                        self._session_id,
                                        position_uuid,
                                    ).create_tool_result_response(
                                        {
                                            "search_key": search_key,
                                            "positions": position_summaries,
                                            "recommendations": recommendations,
                                        }
                                    )
                                except (
                                    json.JSONDecodeError,
                                    AttributeError,
                                    TypeError,
                                ) as e:
                                    self.logger.error(
                                        f"ポジション検索失敗しました: {e}",
                                        exc_info=True,
                                    )
                                    yield ChatStreamReponse(
                                        self._session_id,
                                        position_uuid,
                                    ).create_error_response(
                                        DEFAULT_ERROR_MESSAGE,
                                    )
                # ポジション検索ツールのバリデーションエラーがない限り、終わり
                if position_search_validation_error:
                    self._save_chat(message, result)
                    self._conversation_history = result.to_input_list()
                else:
                    break
        except Exception as e:
            self.logger.error(f"Error streaming chat: {e}", exc_info=True)
            yield ChatStreamReponse(
                self._session_id,
                position_uuid,
            ).create_error_response(f"Error: {e}")

        self._save_chat(message, result)

        # 会話履歴を更新する
        self._conversation_history = result.to_input_list()
        if stop_at_tool_calls:
            # stop_at_tool_callsの内容をself._conversation_historyに追加する
            for _, history in stop_at_tool_calls.items():
                # 0.14 -> 0.16 updated, tool reulst of return_true tool is also contained in result.to_input_list()
                # TODO: If it is a long conversation, it isn't very efficient to remove result from history everytime.
                self._conversation_history = [
                    record
                    for record in self._conversation_history
                    if not (
                        record.get("type") == "function_call_output"
                        and record.get("call_id") == history.tool_call_id
                    )
                ]
                self._conversation_history.append(
                    {
                        "type": "function_call_output",
                        "call_id": history.tool_call_id,
                        "output": POSITION_SEARCH_FAKE_RESULT,
                    }
                )

        yield ChatStreamReponse(
            self._session_id,
            position_uuid,
        ).create_end_response()

    def _save_chat(
        self,
        message: str,
        result: RunResultStreaming,
    ):
        if self._should_save:
            if not self._session_created:
                # セッションはまだ作成されていないので、セッションを作成して、最初の挨拶と今回のユーザインプットをDBに保存する
                # 今回LLMレスポンスは_save_llm_messagesより保存します。
                self._create_session(result.input)
            else:
                # セッションは作成済みなので、ユーザーからのメッセージだけを保存する
                self._save_user_message(message)
            # LLMの返答を保存する
            # result.to_input_list()は普通のツールコールか、ハンドオフか区別できないので、result.new_itemsを使います。
            self._save_llm_messages(result.new_items)
        else:
            # 初めは挨拶なので、セッション作成はしないが、それ以降会話は続く場合保存するので、DB保存フラグをTrueにする
            self._should_save = True

    def _create_session(self, conversation_history: list[TResponseInputItem]):
        """
        挨拶以上の会話した場合、初めてセッションデータをDB保存する

        Args:
            conversation_history: 会話履歴
        """
        self._chat_repository.create_chat_session(
            session_id=self._session_id,
        )
        # 最初の挨拶メッセージ保存
        chat_histories: list[ChatHistory] = []
        for message in conversation_history:
            if message["type"] == "message":
                chat_histories.append(
                    ChatHistory(
                        session_id=self._session_id,
                        active_agent=self._active_agent.name,
                        # TODO: Not sure from when, id is always "__fake_id__"
                        message_id=message["id"] if "id" in message else None,
                        role=message["role"],
                        content=(
                            message["content"]
                            if isinstance(message["content"], str)
                            else message["content"][0]["text"]
                        ),
                    )
                )
            elif message["type"] == "function_call":
                chat_histories.append(
                    ChatHistory(
                        session_id=self._session_id,
                        active_agent=self._active_agent.name,
                        message_id=message["id"] if "id" in message else None,
                        role=LLMMessageRole.TOOL,
                        tool_call_id=message["call_id"],
                        tool_name=message["name"],
                        tool_input=json.loads(message["arguments"]),
                    )
                )
            elif message["type"] == "function_call_output":
                tool_call_id = message["call_id"]
                history = [
                    history
                    for history in chat_histories
                    if history.tool_call_id == tool_call_id
                ]
                if history:
                    history[0].content = message["output"]
                else:
                    self.logger.error(f"tool call id {tool_call_id} is NOT found.")
            elif message["type"] == "reasoning":
                chat_histories.append(
                    ChatHistory(
                        session_id=self._session_id,
                        active_agent=self._active_agent.name,
                        message_id=message["id"] if "id" in message else None,
                        role=LLMMessageRole.REASONING,
                        content=str.join(message["summary"]),
                    )
                )
            else:
                self.logger.error(f"Unsupported message type: {message}")

        self._chat_repository.add_chat_histories(chat_histories)
        self._session_created = True

    def _save_user_message(
        self,
        message: str,
    ):
        """
        ユーザーインプットをDB保存する

        Args:
            message: ユーザーインプット
        """
        self._chat_repository.add_chat_history(
            ChatHistory(
                session_id=self._session_id,
                active_agent=self._active_agent.name,
                role=LLMMessageRole.USER,
                content=message,
            )
        )

    def _save_llm_messages(
        self,
        items: list[RunItem],
    ):
        """
        ユーザーインプットをDB保存する

        Args:
            message: ユーザーインプット
        """
        chat_histories: list[ChatHistory] = []

        for item in items:
            self._active_agent = item.agent
            if isinstance(item, MessageOutputItem):
                chat_histories.append(
                    ChatHistory(
                        session_id=self._session_id,
                        active_agent=self._active_agent.name,
                        message_id=item.raw_item.id,
                        role=LLMMessageRole.ASSISTANT,
                        content=item.raw_item.content[0].text,
                    )
                )
            elif isinstance(item, HandoffCallItem) or isinstance(item, ToolCallItem):
                chat_histories.append(
                    ChatHistory(
                        session_id=self._session_id,
                        active_agent=self._active_agent.name,
                        message_id=item.raw_item.id,
                        role=(
                            LLMMessageRole.TOOL
                            if isinstance(item, ToolCallItem)
                            else LLMMessageRole.HANDOFF
                        ),
                        tool_call_id=item.raw_item.call_id,
                        tool_name=item.raw_item.name,
                        tool_input=json.loads(item.raw_item.arguments),
                    )
                )
            elif isinstance(item, HandoffOutputItem) or isinstance(
                item, ToolCallOutputItem
            ):
                history = [
                    history
                    for history in chat_histories
                    if history.tool_call_id == item.raw_item["call_id"]
                ]
                if history:
                    history[0].content = item.raw_item["output"]
            elif isinstance(item, ReasoningItem):
                chat_histories.append(
                    ChatHistory(
                        session_id=self._session_id,
                        active_agent=self._active_agent.name,
                        message_id=item.raw_item.id,
                        role=LLMMessageRole.REASONING,
                        content=str.join(item.raw_item.summary),
                    )
                )
            else:
                self.logger.error(f"Unsupported item type: {item}")

        self._chat_repository.add_chat_histories(chat_histories)

    def _get_position_detail(
        self,
        position_uuid: str,
    ) -> Tuple[dict, dict, dict, str]:
        """
        キャッシュからポジション詳細、会社詳細、業界詳細を取得
        Args:
            position_uuid: ポジションUUID

        Returns:
            成功時：ポジション詳細、会社詳細、業界詳細
            失敗時：エラーメッセージ
        """
        postion_detail = self._position_repository.get_position_detail(
            self._session_id,
            position_uuid,
        )
        if not postion_detail:
            return (None, None, None, f"Position detail not found: {position_uuid}")

        company_detail = self._position_repository.get_company_detail(
            self._session_id, position_uuid
        )
        if not company_detail:
            return (None, None, None, f"Company detail not found: {position_uuid}")

        business_detail = self._position_repository.get_business_detail(
            self._session_id, position_uuid
        )
        if not business_detail:
            return (None, None, None, f"Business detail not found: {position_uuid}")

        return (postion_detail, company_detail, business_detail, None)
