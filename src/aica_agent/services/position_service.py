import copy
import requests
import uuid

from repositories.position_repo import PositionRepository
from services.base_service import BaseService


class PositionService(BaseService):
    """
    ポジション関連操作(APIリクエストやキャッシュ保存/取得)サービス
    """

    def __init__(
        self,
        position_repository: PositionRepository,
        api_url: str,
    ) -> None:
        super().__init__()

        self._api_url = api_url
        self._position_repository = position_repository

    def get_position_id(
        self,
        session_id: str,
        position_uuid: str,
    ) -> int | None:
        """
        変換後のポジションIDから本当のポジションIDを取得

        Args:
            session_id: セッションID。
            position_uuid: 変換後ポジションID

        Returns:
            本当のポジションID
        """
        position_summary = self._position_repository.get_position_summary(
            session_id, position_uuid
        )
        return position_summary["ID"] if position_summary else None

    def get_company_id(
        self,
        session_id: str,
        position_uuid: str,
    ) -> int | None:
        """
        変換後のポジションIDから本当の会社IDを取得

        Args:
            session_id: セッションID。
            position_uuid: 変換後ポジションID

        Returns:
            本当の会社ID
        """
        position_summary = self._position_repository.get_position_summary(
            session_id, position_uuid
        )
        return position_summary["CompanyID"] if position_summary else None

    def get_business_id(
        self,
        session_id: str,
        position_uuid: str,
    ) -> int | None:
        """
        変換後のポジションIDから本当の業界IDを取得

        Args:
            session_id: セッションID。
            position_uuid: 変換後ポジションID

        Returns:
            本当の業界ID
        """
        position_summary = self._position_repository.get_position_summary(
            session_id, position_uuid
        )
        return position_summary["BusinessID"] if position_summary else None

    def remove_session(self, session_id):
        """
        キャッシュから指定セッションのデータを削除する

        Args:
            session_id: セッションID。
        """
        self._position_repository.remove_session(session_id)

    def get_position_detail(
        self,
        session_id: str,
        position_uuid: str,
    ) -> dict:
        """
        ポジション詳細APIを呼び出して、ポジション詳細を取得してキャッシュに保存する

        Args:
            session_id: セッションID。
            position_uuid: 変換後ポジションID

        Returns:
            ポジション詳細
        """
        position_id = self.get_position_id(session_id, position_uuid)
        if not position_id:
            return None

        api_url = f"{self._api_url}/positions/{position_id}"
        headers = {
            "CHAT_SESSION_ID": session_id,
            # TODO: not generate a new request_id but use request_id from client
            "TOOL_REQUEST_ID": str(uuid.uuid4()),
        }
        response = requests.post(api_url, headers=headers)

        if response.status_code == 200:
            position_detail = response.json()
            self._position_repository.save_position_detail(
                session_id, position_uuid, position_detail
            )
            return position_detail
        else:
            return None

    def get_company_detail(
        self,
        session_id: str,
        position_uuid: str,
    ):
        """
        会社詳細APIを呼び出して、会社詳細を取得してキャッシュに保存する

        Args:
            session_id: セッションID。
            position_uuid: 変換後ポジションID

        Returns:
            会社詳細
        """
        company_id = self.get_company_id(session_id, position_uuid)
        if not company_id:
            return None

        api_url = f"{self._api_url}/companies/{company_id}"
        headers = {
            "CHAT_SESSION_ID": session_id,
            "TOOL_REQUEST_ID": str(uuid.uuid4()),
        }
        response = requests.get(api_url, headers=headers)

        if response.status_code == 200:
            company_detail = response.json()
            self._position_repository.save_company_detail(
                session_id, position_uuid, company_detail
            )
            return company_detail
        else:
            return None

    def get_business_detail(
        self,
        session_id: str,
        position_uuid: str,
    ):
        """
        業界詳細APIを呼び出して、業界詳細を取得してキャッシュに保存する

        Args:
            session_id: セッションID。
            position_uuid: 変換後ポジションID

        Returns:
            業界詳細
        """
        business_id = self.get_business_id(session_id, position_uuid)
        if not business_id:
            return None

        api_url = f"{self._api_url}/businesses/{business_id}"
        headers = {
            "CHAT_SESSION_ID": session_id,
            "TOOL_REQUEST_ID": str(uuid.uuid4()),
        }
        response = requests.get(api_url, headers=headers)

        if response.status_code == 200:
            business_detail = response.json()
            self._position_repository.save_business_detail(
                session_id,
                position_uuid,
                business_detail,
            )
            return business_detail
        else:
            return None


    def get_position_recommendation(
        self,
        session_id: str,
        theme: str,
    ) -> list[dict] | None:
        """
        APIサーバーからおすすめポジションを取得する

        Args:
            session_id: セッションID。
            theme: おすすめリンクのマッピングキー
            search_conditions: ユーザーの検索条件

        Returns:
            おすすめのポジションリスト
        """
        thema = self._position_repository.get_position_recommendation_thema(
            session_id,
            theme,
        )
        if not thema:
            return None
        
        search_conditions = self._position_repository.get_user_preferences(
            session_id,
        )

        api_url = f"{self._api_url}/positions/recommendations/{thema}"
        headers = {
            "CHAT_SESSION_ID": session_id,
            "TOOL_REQUEST_ID": str(uuid.uuid4()),
        }
        response = requests.post(api_url, json=search_conditions, headers=headers)

        if response.status_code == 200:
            position_summaries = response.json()
            if "Positions" not in position_summaries:
                return None

            # TODO: chat_service.pyと同じロジックなので、共通化する
            position_summaries = position_summaries["Positions"]
            position_id_2_uuid = self._position_repository.save_position_summaries(
                session_id,
                copy.deepcopy(position_summaries),
            )

            for position in position_summaries:
                if "ID" in position:
                    position["ID"] = position_id_2_uuid[
                        position["ID"]
                    ]
                if "CompanyID" in position:
                    position.pop("CompanyID", None)
                if "BusinessID" in position:
                    position.pop("BusinessID", None)

            return position_summaries
        else:
            return None
