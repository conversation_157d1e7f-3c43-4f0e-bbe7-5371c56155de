from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, DateTime, Foreign<PERSON>ey, Integer, String
from sqlalchemy.orm import Mapped, mapped_column

from database import Base


class ChatHistory(Base):
    __tablename__ = "chat_histories"

    id = Column(Integer, primary_key=True)
    session_id: Mapped[String] = mapped_column(ForeignKey("chat_sessions.session_id"))
    active_agent = Column(String)
    message_id = Column(String)
    role = Column(String)
    content = Column(String)
    tool_call_id = Column(String, nullable=True)
    tool_name = Column(String, nullable=True)
    tool_input = Column(JSON, nullable=True)
    deleted_at = Column(DateTime, nullable=True)
