from typing import List
from sqlalchemy import Column, Integer, String
from sqlalchemy.orm import Mapped, relationship

from database import Base

from .chat_history import ChatHistory


class ChatSession(Base):
    __tablename__ = "chat_sessions"

    id = Column(Integer, primary_key=True)
    session_id = Column(String, unique=True)
    summary = Column(String, nullable=True)

    histories: Mapped[List[ChatHistory]] = relationship(
        primaryjoin="ChatSession.session_id == ChatHistory.session_id"
    )
