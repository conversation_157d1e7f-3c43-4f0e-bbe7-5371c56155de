import json
import logging
from typing import Any, Optional
import uuid
from fastapi import (
    APIRouter,
    Depends,
    WebSocket,
    WebSocketDisconnect,
    status,
    HTTPException,
)
from dependency_injector.wiring import Provide, inject

from agents import trace

from containers import Container
from repositories.llm_repo import LLMModel
from services.chat_service import ChatService, ChatStreamReponse, MessageType
from services.position_service import PositionService
from utils.logging import set_session_id, clear_session_id

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/agent")


@router.get("/health")
def health():
    return {"status": "OK"}


@router.get("/positions/{session_id}/{position_id}")
@inject
def position_detail(
    session_id: str,
    position_id: str,
    position_service: PositionService = Depends(Provide[Container.position_service]),
):
    detail = position_service.get_position_detail(session_id, position_id)
    if detail:
        return detail
    else:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Position not found"
        )


@router.get("/companies/{session_id}/{position_id}")
@inject
def company_detail(
    session_id: str,
    position_id: str,
    position_service: PositionService = Depends(Provide[Container.position_service]),
):
    detail = position_service.get_company_detail(session_id, position_id)
    if detail:
        return detail
    else:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Company not found"
        )


@router.get("/businesses/{session_id}/{position_id}")
@inject
def business_detail(
    session_id: str,
    position_id: str,
    position_service: PositionService = Depends(Provide[Container.position_service]),
):
    detail = position_service.get_business_detail(session_id, position_id)
    if detail:
        return detail
    else:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Business not found"
        )


@router.get("/positions/recommendations/{session_id}/{search_key}/{theme}")
@inject
def position_recommendations(
    session_id: str,
    search_key: str,
    theme: str,
    position_service: PositionService = Depends(Provide[Container.position_service]),
):
    positions = position_service.get_position_recommendation(
        session_id,
        theme,
    )
    if not positions:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Recommendation not found"
        )
    return {
        "search_key": search_key,
        "positions": positions,
    }


@router.websocket("/chat")
@inject
async def chat(
    websocket: WebSocket,
    session_id: Optional[str] = None,
    chat_service: ChatService = Depends(Provide[Container.chat_service]),
    # position_service: Any = Depends(Provide[Container.position_service]),
):

    await websocket.accept()

    try:
        if session_id is None:
            session_id = str(uuid.uuid4())
        set_session_id(session_id)
        logger.debug("Session ID: %s", session_id)

        await handle_chat_session(websocket, session_id, chat_service)
    except WebSocketDisconnect as e:
        logger.warning("Websocket disconnected: %s", e, exc_info=True)
    except Exception as e:
        logger.error("Unexpected error: %s", e, exc_info=True)
    finally:
        clear_session_id()
        # TODO: don't clear session immediately when websocket is disconnected.
        # position_service.remove_session(session_id)


async def handle_chat_session(
    websocket: WebSocket,
    session_id: str,
    chat_service: ChatService,
):
    if not await chat_service.init_session(session_id, LLMModel.OPENAI_GPT_4_1.value):
        # if not await chat_service.init_session(session_id, LLMModel.BEDROCK_CLAUDE_V1.value):
        await send_error_response(websocket, session_id)
    else:
        await process_chat_messages(websocket, session_id, chat_service)


async def send_error_response(
    websocket: WebSocket,
    session_id: str,
):
    error_response = ChatStreamReponse(
        session_id,
    ).create_error_response("Failed to initialize session")
    await websocket.send_text(error_response.model_dump_json())


async def process_chat_messages(
    websocket: WebSocket,
    session_id: str,
    chat_service: ChatService,
):
    with trace("AICA workflow", trace_id=f"trace_{session_id}"):
        while True:
            try:
                message = await websocket.receive_text()
                input = json.loads(message)
                input.setdefault("prev_page", "")
                input.setdefault("current_page", "")
                input.setdefault("position_id", "")
                input.setdefault("message", "")
                if input["message"]:
                    async for chunk in chat_service.chat(input):
                        await websocket.send_text(chunk.model_dump_json())
            except json.JSONDecodeError as e:
                logger.error("Invalid JSON: %s", e, exc_info=True)
