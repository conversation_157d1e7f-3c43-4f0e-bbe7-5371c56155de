from contextlib import AbstractContextManager
from typing import Callable
from sqlalchemy import select, text
from sqlalchemy.orm import Session, joinedload, aliased

from domain.enitites.chat_history import ChatHistory
from domain.enitites.chat_session import ChatSession


class ChatRepository:
    def __init__(
        self,
        session_factory: Callable[..., AbstractContextManager[Session]],
    ) -> None:
        self._session_factory = session_factory

    def init_chat_session(
        self,
        chat_session_id: str,
    ) -> ChatSession | None:
        """
        chat_session_idに対応する会話履歴も取得する。

        Args:
            chat_session_id: セッションID

        Returns:
            会話履歴 or None
        """
        with self._session_factory() as session:
            HistoryAlias = aliased(ChatHistory)
            stmt = (
                select(ChatSession)
                .options(
                    joinedload(
                        ChatSession.histories.of_type(HistoryAlias).and_(
                            HistoryAlias.deleted_at.is_(None)
                        )
                    )
                )
                .filter(ChatSession.session_id == chat_session_id)
                .order_by(HistoryAlias.id)
            )
            chat_session = session.scalars(stmt).unique().first()
            if chat_session:
                return chat_session
        return None

    def create_chat_session(
        self,
        session_id: str,
    ):
        """
        会話セッション作成する。

        Args:
            chat_session_id: セッションID
        """
        with self._session_factory() as session:
            chat_session = ChatSession(
                session_id=session_id,
            )
            session.add(chat_session)
            session.commit()

    def add_chat_histories(
        self,
        chat_histories: list[ChatHistory],
    ):
        """
        複数会話を追加する。

        Args:
            chat_histories: 会話履歴
        """
        with self._session_factory() as session:
            for chat_history in chat_histories:
                session.add(chat_history)
                session.commit()

    def add_chat_history(
        self,
        chat_history: ChatHistory,
    ) -> None:
        """
        １つの会話を追加する。

        Args:
            chat_history: 会話履歴
        """
        with self._session_factory() as session:
            session.add(chat_history)
            session.commit()
