import logging
from typing import Any
import uuid
from utils import sync_dict


# 本当のポジションIDを外部公開しないため、ポジションツール検索結果をメモリに保存し、ユーザーがポジションの「詳細を見る」を押すときに、ここから本当のポジションIDを取得し、APIサーバからポジション詳細を取得する。
# TODO: セッション再開の場合、もし以前の履歴はまだ見れるなら、表示するたびに、このマッピングを作成する必要があります。
# TODO: エラーがあって接続が切れて再属した場合、セッションはクリアされたので、ポジションカードは画面に表示されていても、実はクリックできない。
class PositionRepository:
    """
    セッション毎にポジション関連情報のキャッシュ
    """

    def __init__(self):
        """
        下記のポジション情報キャッシュ初期化
        ・ポジション検索結果
        ・ポジション詳細表示に関連する情報
            ・ポジション詳細
            ・会社詳細
            ・業界詳細
        """
        self._logger = logging.getLogger(
            f"{self.__class__.__module__}.{self.__class__.__name__}"
        )
        # 違うセッションに同じポジションを持つ可能性がありますので、メモリ利用は良くないかもしれないが、
        # 共通メモリを持つと、処理ロジック（特に有効期限の管理）は複雑になるので、一旦やめた。
        # str, dict[str, dict]　→　セッションID, dict[ポジションUUID, データ]
        self._position_summaries = sync_dict.SynchronizedDict[str, dict[str, dict]]()
        self._position_details = sync_dict.SynchronizedDict[str, dict[str, dict]]()
        self._company_details = sync_dict.SynchronizedDict[str, dict[str, dict]]()
        self._business_details = sync_dict.SynchronizedDict[str, dict[str, dict]]()
        self._position_recommendations = sync_dict.SynchronizedDict[
            str, dict[str, dict]
        ]()
        self._user_preferences = sync_dict.SynchronizedDict[str, dict[str, Any]]()

    def save_position_summaries(
        self,
        session_id: str,
        position_summaries: dict,
    ) -> dict[int, str]:
        """
        ポジション検索結果をキャッシュし、ポジションIDマッピングを作成する。

        Args:
            session_id: セッションID。
            position_summaries: 検索結果

        Returns:
            検索結果のポジションIDマッピング
        """
        session_data = self._position_summaries.setdefault(session_id, {})
        saved_position_ids = [pos_dict["ID"] for pos_dict in session_data.values()]
        session_data.update(
            {
                str(uuid.uuid4()): position
                for position in position_summaries
                if position["ID"] not in saved_position_ids
            }
        )
        return {
            position["ID"]: position_uuid
            for position_uuid, position in session_data.items()
        }

    def get_position_summary(
        self,
        session_id,
        position_uuid,
    ) -> dict[str, dict] | None:
        """
        キャッシュからポジション検索結果を取得

        Args:
            session_id: セッションID。
            position_uuid: 変換後ポジションID

        Returns:
            position_uuidに対応するポジション検索結果
        """
        saved_positions = self._position_summaries.setdefault(session_id, {})
        return (
            saved_positions[position_uuid] if position_uuid in saved_positions else None
        )

    def save_position_detail(
        self,
        session_id: str,
        position_uuid: str,
        position_detail: dict,
    ):
        """
        ポジション詳細APIレスポンスをキャッシュする

        Args:
            session_id: セッションID。
            position_uuid: 変換後ポジションID
            position_detail: ポジション詳細APIレスポンス
        """
        position_details = self._position_details.setdefault(session_id, {})
        position_details[position_uuid] = position_detail

    def get_position_detail(
        self,
        session_id: str,
        position_uuid: str,
    ) -> dict | None:
        """
        キャッシュからポジション詳細APIレスポンスを取得

        Args:
            session_id: セッションID。
            position_uuid: 変換後ポジションID

        Returns:
            position_uuidに対応するポジション詳細APIレスポンス
        """
        position_details = self._position_details.setdefault(session_id, {})
        return (
            position_details.get(position_uuid)
            if position_uuid in position_details
            else None
        )

    def save_company_detail(
        self,
        session_id: str,
        position_uuid: str,
        company_detail: dict,
    ):
        """
        会社詳細APIレスポンスをキャッシュする

        Args:
            session_id: セッションID。
            position_uuid: 変換後ポジションID
            company_detail: 会社詳細APIレスポンス
        """
        company_details = self._company_details.setdefault(session_id, {})
        company_details[position_uuid] = company_detail

    def get_company_detail(
        self,
        session_id: str,
        position_uuid: str,
    ) -> dict | None:
        """
        キャッシュから会社詳細APIレスポンスを取得

        Args:
            session_id: セッションID。
            position_uuid: 変換後ポジションID

        Returns:
            position_uuidに対応する会社詳細APIレスポンス
        """
        company_details = self._company_details.setdefault(session_id, {})
        return (
            company_details.get(position_uuid)
            if position_uuid in company_details
            else None
        )

    def save_business_detail(
        self,
        session_id: str,
        position_uuid: str,
        business_detail: dict,
    ):
        """
        業界詳細APIレスポンスをキャッシュする

        Args:
            session_id: セッションID。
            position_uuid: 変換後ポジションID
            business_detail: 業界詳細APIレスポンス
        """
        business_details = self._business_details.setdefault(session_id, {})
        business_details[position_uuid] = business_detail

    def get_business_detail(
        self,
        session_id: str,
        position_uuid: str,
    ) -> dict | None:
        """
        キャッシュから業界詳細APIレスポンスを取得

        Args:
            session_id: セッションID。
            position_uuid: 変換後ポジションID

        Returns:
            position_uuidに対応する業界詳細APIレスポンス
        """
        business_details = self._business_details.setdefault(session_id, {})
        return (
            business_details.get(position_uuid)
            if position_uuid in business_details
            else None
        )

    def save_position_recommendations(
        self,
        session_id: str,
        recommendation_themes: list[str],
    ) -> dict[str, str]:
        """
        おすすめのリンクマッピング作成

        Args:
            session_id: セッションID。
            recommendation_themes: おすすめパスリスト

        Returns:
            レコメンドリンクマッピング
        """
        links = {theme: str(uuid.uuid4()) for theme in recommendation_themes}
        session_data = self._position_recommendations.setdefault(session_id, {})
        session_data.update({uuid_str: theme for theme, uuid_str in links.items()})
        return {theme: uuid_str for theme, uuid_str in links.items()}

    def get_position_recommendation_thema(
        self,
        session_id: str,
        theme: str,
    ) -> str | None:
        """
        おすすめのリンクマッピングからパスを取得

        Args:
            session_id: セッションID。
            theme: おすすめID

        Returns:
            おすすめIDに対応するパス
        """
        session_data = self._position_recommendations.setdefault(session_id, {})
        return session_data.get(theme) if theme in session_data else None

    def save_user_preference(
        self,
        session_id: str,
        preference: dict[str, Any],
    ):
        """
        ユーザーの検索条件をキャッシュする

        Args:
            session_id: セッションID。
            preference: ユーザーの検索条件
        """
        self._logger.debug(f"save_user_preference: {preference}")
        session_data = self._user_preferences.setdefault(session_id, {})
        session_data.update(preference)

    def get_user_preferences(
        self,
        session_id: str,
    ) -> dict | None:
        """
        キャッシュからユーザーの検索条件を取得

        Args:
            session_id: セッションID。

        Returns:
            ユーザーの検索条件
        """
        session_data = self._user_preferences.setdefault(session_id, {})
        return session_data if session_data else {}

    def remove_session(
        self,
        session_id: str,
    ):
        """
        キャッシュから指定セッションのデータを削除する

        Args:
            session_id: セッションID。
        """
        self._position_summaries.pop(session_id)
