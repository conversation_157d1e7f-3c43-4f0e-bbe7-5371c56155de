from enum import StrEnum
from agents import Agent, ModelSettings
from dependency_injector import resources
import logging

from agents.extensions.models.litellm_model import LitellmModel

from agents.mcp import MCPServerSse
from agents.mcp.util import MCPUtil

from repositories.agent_repo import AgentRepository
from repositories.mcp_repo import McpRepository


class LLMModel(StrEnum):
    """
    LLMモデル名
    [src/aica_agent/config.yml]の[model_list]の[model_name]と１対１
    """

    BEDROCK_CLAUDE_V1 = "bedrock-claude-v1"
    OPENAI_GPT_4_1 = "openai-gpt-4.1"


class NotSupportedProvider(Exception):
    """
    LLMModelにないモデル
    """

    def __init__(self, message):
        super().__init__(message)


class NotImplementedProvider(Exception):
    def __init__(self, message):
        super().__init__(message)


class AgentName(StrEnum):
    """
    エージェント名
    テーブル[agents]のカラム[name]となります。
    動的にアクティブエージェントを切り替えるときに利用するためソースにも定義しています。
    """

    DEFAULT_AGENT = "DefaultAgent"
    CAREER_ADVISOR = "CareerAdvisor"
    POSITION_GUIDE = "PositionGuide"


class LLMRepository(resources.AsyncResource):
    def __init__(self) -> None:
        self._logger = logging.getLogger(
            f"{self.__class__.__module__}.{self.__class__.__name__}"
        )

        self._mcp_server = None
        self._agents: dict[str, dict[str, tuple[Agent, bool]]] = {}

    async def init(
        self,
        mcp_url,
        model_list,
        agent_repository: AgentRepository,
        mcp_repository: McpRepository,
    ):
        """
        LLMRepository初期化
        ・MCPサーバ接続
        ・ワークフロー初期化（エージェントとハンドオフ関係、ツール）

        Args:
            mcp_url: MCPサーバーURL
            model_list: LLMモデル一覧
            agent_repository: エージェントリポジトリ
            mcp_repository: MCPリポジトリ
        """
        self._mcp_server = MCPServerSse(
            name="AICA Server",
            params={
                "url": mcp_url,
            },
            client_session_timeout_seconds=60,
        )
        await self._mcp_server.__aenter__()
        await self._init_agents(model_list, agent_repository, mcp_repository)

        return self

    async def shutdown(self):
        """
        LLMRepository終了処理
        ・MCPサーバ切断
        """
        await self._mcp_server.__aexit__()

    # TODO: When MCP server restarts, Agent server also needs to be restarted, otherwise MCP tool calling will fail.
    # Reconnect to MCP server and reinitialize agents.
    async def reconnect(self):
        """
        MCPサーバ再起動などのため、接続切れば場合、エージェント初期化時のMCPサーバ接続はもう使えなくなりますので、
        ツール呼び出しができなくなります。
        そのため、再接続の手段が必要ですが、まだやってない
        """
        await self._mcp_server.__aexit__()
        await self._mcp_server.__aenter__()
        await self._init_agents(self._model_list, self._agent_repository)

    async def _init_agents(
        self,
        model_list,
        agent_repository: AgentRepository,
        mcp_repository: McpRepository,
    ):
        """
        src/aica_agent/config.ymlの[model_list]をもとにワークフロー初期化（エージェントとハンドオフ関係、ツール）

        Args:
            model_list: LLMモデル一覧。src/aica_agent/config.ymlの[model_list]
            agent_repository: エージェントリポジトリ（エージェント設定；デフォルトエージェントや紐づいたツール名など）
            mcp_repository: MCPリポジト（エージェントのシステムプロンプト、ツール定義）
        """
        tools = await self._mcp_server.list_tools()
        self._logger.debug(f"Tools: {tools}")
        tools = await MCPUtil.get_all_function_tools([self._mcp_server], True)
        agents = agent_repository.get_agents()
        for model in model_list:
            if model["model_name"] not in [e.value for e in LLMModel]:
                self._logger.error(f"Unsupported model: {model.model_name}")
                continue
            model_name = model["model_settings"]["model"]
            model_settings = ModelSettings(
                temperature=model["model_settings"]["temperature"],
                top_p=model["model_settings"]["top_p"],
            )
            react_agents = {}
            for agent in agents:
                prompt = mcp_repository.get_prompt_by_agentname(agent.name)
                if prompt is None:
                    self.logger.error(f"Prompt not found for agent: {agent.name}")
                    return False
                else:
                    agent_prompt = prompt
                    self._logger.debug(
                        f"System Prompt for {agent.name}: {agent_prompt}"
                    )

                agent_tool_names = [tool.tool_name for tool in agent.tools]
                agent_tools = [tool for tool in tools if tool.name in agent_tool_names]
                react_agent = Agent(
                    model=LitellmModel(model_name),
                    model_settings=model_settings,
                    name=agent.name,
                    instructions=agent_prompt,
                    tools=agent_tools,
                )
                stop_at_tool_names = [
                    tool.tool_name
                    for tool in agent.tools
                    if tool.tool and tool.tool.return_direct
                ]
                if stop_at_tool_names:
                    react_agent.tool_use_behavior = {
                        "stop_at_tool_names": stop_at_tool_names,
                    }

                react_agents[agent.name] = (
                    react_agent,
                    agent.next_agents,
                    agent.default_agent,
                )

            for _, (agent, next_agents, _) in react_agents.items():
                if next_agents:
                    agent.handoffs = [
                        react_agents[next_agent.dest_agent.name][0]
                        for next_agent in next_agents
                    ]

            self._agents[model["model_name"]] = {
                agent_name: (agent, default_agent)
                for agent_name, (agent, _, default_agent) in react_agents.items()
            }

    def active_agent(
        self,
        provider: str,
        active_agent_name: str = None,
    ) -> Agent:
        """
        アクティブなエージェントを取得する。

        Args:
            provider: LLMプロバイダー
            active_agent_name: アクティブなエージェント名。Noneの場合はデフォルトエージェントを返す。

        Returns:
            アクティブなエージェント
        """
        if provider == LLMModel.BEDROCK_CLAUDE_V1:
            agents = self._agents[LLMModel.BEDROCK_CLAUDE_V1.value]
            if active_agent_name:
                return agents[active_agent_name][0]
            else:
                return [
                    agent
                    for _, (agent, default_agent) in agents.items()
                    if default_agent
                ][0]
        elif provider == LLMModel.OPENAI_GPT_4_1:
            agents = self._agents[LLMModel.OPENAI_GPT_4_1.value]
            if active_agent_name:
                return agents[active_agent_name][0]
            else:
                return [
                    agent
                    for _, (agent, default_agent) in agents.items()
                    if default_agent
                ][0]
        else:
            self._logger.exception(f"Unsupported provider: {provider}")
            raise NotSupportedProvider(f"Unsupported provider: {provider}")
