import asyncio
from contextlib import Abs<PERSON><PERSON><PERSON>xt<PERSON>anager
from typing import Callable
from sqlalchemy import select
from sqlalchemy.orm import Session, joinedload, aliased
from concurrent.futures import ThreadPoolExecutor

from domain.enitites.chat_history import ChatHistory
from domain.enitites.chat_session import ChatSession


class AsyncChatRepository:
    """
    Async wrapper for ChatRepository to prevent blocking the event loop
    """
    
    def __init__(
        self,
        session_factory: Callable[..., AbstractContextManager[Session]],
        executor: ThreadPoolExecutor = None,
    ) -> None:
        self._session_factory = session_factory
        self._executor = executor or ThreadPoolExecutor(max_workers=10)

    async def init_chat_session(
        self,
        chat_session_id: str,
    ) -> ChatSession | None:
        """
        chat_session_idに対応する会話履歴も取得する。(Async version)

        Args:
            chat_session_id: セッションID

        Returns:
            会話履歴 or None
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self._executor, 
            self._sync_init_chat_session, 
            chat_session_id
        )

    def _sync_init_chat_session(self, chat_session_id: str) -> ChatSession | None:
        """Synchronous implementation of init_chat_session"""
        with self._session_factory() as session:
            HistoryAlias = aliased(ChatHistory)
            stmt = (
                select(ChatSession)
                .options(
                    joinedload(
                        ChatSession.histories.of_type(HistoryAlias).and_(
                            HistoryAlias.deleted_at.is_(None)
                        )
                    )
                )
                .filter(ChatSession.session_id == chat_session_id)
                .order_by(HistoryAlias.id)
            )
            chat_session = session.scalars(stmt).unique().first()
            if chat_session:
                return chat_session
        return None

    async def create_chat_session(
        self,
        session_id: str,
    ):
        """
        会話セッション作成する。(Async version)

        Args:
            session_id: セッションID
        """
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(
            self._executor, 
            self._sync_create_chat_session, 
            session_id
        )

    def _sync_create_chat_session(self, session_id: str):
        """Synchronous implementation of create_chat_session"""
        with self._session_factory() as session:
            chat_session = ChatSession(
                session_id=session_id,
            )
            session.add(chat_session)
            session.commit()

    async def add_chat_histories(
        self,
        chat_histories: list[ChatHistory],
    ):
        """
        複数会話を追加する。(Async version)

        Args:
            chat_histories: 会話履歴
        """
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(
            self._executor, 
            self._sync_add_chat_histories, 
            chat_histories
        )

    def _sync_add_chat_histories(self, chat_histories: list[ChatHistory]):
        """Synchronous implementation of add_chat_histories"""
        if not chat_histories:
            return

        with self._session_factory() as session:
            # Optimize: Use bulk insert instead of individual commits
            session.add_all(chat_histories)
            session.commit()

    async def add_chat_history(
        self,
        chat_history: ChatHistory,
    ) -> None:
        """
        １つの会話を追加する。(Async version)

        Args:
            chat_history: 会話履歴
        """
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(
            self._executor, 
            self._sync_add_chat_history, 
            chat_history
        )

    def _sync_add_chat_history(self, chat_history: ChatHistory) -> None:
        """Synchronous implementation of add_chat_history"""
        with self._session_factory() as session:
            session.add(chat_history)
            session.commit()

    async def close(self):
        """Clean up the executor"""
        if self._executor:
            self._executor.shutdown(wait=True)
