# Performance Tuning Guide

## Problem
The server was getting stuck when handling multiple concurrent client connections due to insufficient worker processes and database connection pooling.

## Root Causes
1. **Single Worker Process**: The server was configured with only 1 Gunicorn worker (`-w 1`), creating a bottleneck
2. **No Database Connection Pooling**: Database connections weren't properly pooled, causing connection exhaustion
3. **No Timeout Configuration**: Long-running requests could block workers indefinitely
4. **Blocking Database Operations**: Synchronous database calls in async functions blocked the event loop

## Solutions Implemented

### 1. Increased Worker Processes
- **Before**: `gunicorn -w 1`
- **After**: `gunicorn -w 4` (configurable via `AICA_AGENT_WORKERS`)

### 2. Enhanced Gunicorn Configuration
Added the following parameters:
- `--worker-connections 1000`: Maximum concurrent connections per worker
- `--max-requests 1000`: Restart workers after handling this many requests (prevents memory leaks)
- `--max-requests-jitter 50`: Add randomness to worker restarts
- `--preload`: Load application before forking workers (better memory usage)
- `--timeout 300`: Request timeout in seconds

### 3. Database Connection Pooling
Enhanced SQLAlchemy engine configuration:
```python
create_engine(
    db_url, 
    pool_size=20,          # Base connection pool size
    max_overflow=30,       # Additional connections on demand
    pool_timeout=30,       # Timeout for getting connection
    pool_recycle=3600,     # Recycle connections after 1 hour
    pool_pre_ping=True,    # Validate connections before use
)
```

### 4. Async Database Operations
Replaced synchronous database operations with async versions:
- Created `AsyncChatRepository` that uses `ThreadPoolExecutor` to run database operations
- Updated `ChatService` to use async database calls
- Optimized bulk database operations to reduce individual commits
- Prevents blocking the event loop during database operations

## Configuration Options

### Environment Variables
Add these to your `.env.local` file:

```bash
# Server Performance Configuration
AICA_AGENT_WORKERS=4                    # Number of worker processes
AICA_AGENT_WORKER_CONNECTIONS=1000      # Connections per worker
AICA_AGENT_MAX_REQUESTS=1000            # Requests before worker restart
AICA_AGENT_TIMEOUT=300                  # Request timeout (seconds)
```

### Recommended Settings by Server Size

#### Small Server (2 CPU cores, 4GB RAM)
```bash
AICA_AGENT_WORKERS=2
AICA_AGENT_WORKER_CONNECTIONS=500
AICA_AGENT_MAX_REQUESTS=500
AICA_AGENT_TIMEOUT=180
```

#### Medium Server (4 CPU cores, 8GB RAM)
```bash
AICA_AGENT_WORKERS=4
AICA_AGENT_WORKER_CONNECTIONS=1000
AICA_AGENT_MAX_REQUESTS=1000
AICA_AGENT_TIMEOUT=300
```

#### Large Server (8+ CPU cores, 16GB+ RAM)
```bash
AICA_AGENT_WORKERS=8
AICA_AGENT_WORKER_CONNECTIONS=2000
AICA_AGENT_MAX_REQUESTS=2000
AICA_AGENT_TIMEOUT=300
```

## Monitoring and Troubleshooting

### Key Metrics to Monitor
1. **Response Time**: Should remain consistent under load
2. **Database Connection Pool**: Monitor active/idle connections
3. **Worker Process Health**: Check for worker restarts
4. **Memory Usage**: Monitor for memory leaks

### Common Issues and Solutions

#### High Memory Usage
- Reduce `AICA_AGENT_MAX_REQUESTS` to restart workers more frequently
- Monitor for memory leaks in application code

#### Database Connection Errors
- Increase database `pool_size` and `max_overflow`
- Check database server connection limits

#### Slow Response Times
- Increase `AICA_AGENT_WORKERS` if CPU usage is low
- Optimize database queries
- Consider caching for frequently accessed data

## Testing the Improvements

### Load Testing
Use tools like `wrk` or `ab` to test concurrent connections:

```bash
# Test with 100 concurrent connections for 30 seconds
wrk -t12 -c100 -d30s --timeout 30s http://localhost:8000/agent/health

# Apache Bench alternative
ab -n 1000 -c 50 http://localhost:8000/agent/health
```

### WebSocket Load Testing
For WebSocket endpoints, use specialized tools:
```bash
# Install websocket testing tool
npm install -g websocket-bench

# Test WebSocket connections
websocket-bench -a 50 -c 10 ws://localhost:8000/agent/chat
```

## Deployment

After making these changes, restart the server:
```bash
./start_server.sh
```

The server will now handle multiple concurrent connections efficiently without getting stuck.

## Automated Testing

A test script is provided to verify the improvements:

```bash
# Install required dependencies
pip install websockets requests

# Run the concurrent connection test
python test_concurrent_connections.py
```

This test will:
1. Check server health
2. Create 10 concurrent WebSocket connections
3. Send messages and verify responses
4. Test HTTP endpoints under load
5. Report success/failure rates

Expected results after optimization:
- All WebSocket connections should succeed
- Response times should be consistent
- No connection timeouts or server hangs
