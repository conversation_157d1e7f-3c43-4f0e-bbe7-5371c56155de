name: ai-ca
services:
  agent-server:
    container_name: agent-server
    build:
      context: ..
      dockerfile: docker/Dockerfile
    command: gunicorn -w ${AICA_AGENT_WORKERS:-4} -k uvicorn.workers.UvicornWorker --worker-connections ${AICA_AGENT_WORKER_CONNECTIONS:-1000} --max-requests ${AICA_AGENT_MAX_REQUESTS:-1000} --max-requests-jitter 50 --preload --timeout ${AICA_AGENT_TIMEOUT:-300} application:app -b 0.0.0.0:${AICA_AGENT_PORT}
    ports:
      - "${AICA_AGENT_PORT}:${AICA_AGENT_PORT}"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - AICA_AGENT_PORT=${AICA_AGENT_PORT}
      - AICA_AGENT_DB_HOST=${AICA_AGENT_DB_HOST}
      - AICA_AGENT_DB_PORT=${AICA_AGENT_DB_PORT}
      - AICA_AGENT_DB_USER=${AICA_AGENT_DB_USER}
      - AICA_AGENT_DB_PASSWORD=${AICA_AGENT_DB_PASSWORD}
      - AICA_AGENT_DB_NAME=${AICA_AGENT_DB_NAME}
      - AICA_AGENT_DB_SSLMODE=${AICA_AGENT_DB_SSLMODE}
      - AICA_AGENT_MCP_ENDPOINT=${AICA_AGENT_MCP_ENDPOINT}
      - AICA_AGENT_API_ENDPOINT=${AICA_AGENT_API_ENDPOINT}
      - OPENAI_AGENTS_DISABLE_TRACING=${OPENAI_AGENTS_DISABLE_TRACING}
      - AICA_AGENT_WORKERS=${AICA_AGENT_WORKERS:-4}
      - AICA_AGENT_WORKER_CONNECTIONS=${AICA_AGENT_WORKER_CONNECTIONS:-1000}
      - AICA_AGENT_MAX_REQUESTS=${AICA_AGENT_MAX_REQUESTS:-1000}
      - AICA_AGENT_TIMEOUT=${AICA_AGENT_TIMEOUT:-300}
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost/agent/health || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5
