#!/usr/bin/env python3
"""
Debug server script with connection monitoring and better error handling.
Use this for debugging connection issues instead of the basic uvicorn command.
"""

import asyncio
import logging
import signal
import sys
import time
from typing import Dict, Set
import uvicorn
from uvicorn.config import Config
from uvicorn.server import Server

# Configure logging for debugging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class DebugServer(Server):
    """Enhanced server with connection monitoring"""
    
    def __init__(self, config: Config):
        super().__init__(config)
        self.active_connections: Set[str] = set()
        self.connection_count = 0
        self.start_time = time.time()
        
    async def startup(self, sockets=None):
        """Enhanced startup with monitoring"""
        logger.info("Starting debug server with connection monitoring...")
        await super().startup(sockets)
        
        # Start connection monitoring task
        asyncio.create_task(self.monitor_connections())
        
    async def monitor_connections(self):
        """Monitor active connections and log statistics"""
        while True:
            try:
                uptime = time.time() - self.start_time
                logger.info(
                    f"Server Stats - Uptime: {uptime:.1f}s, "
                    f"Active Connections: {len(self.active_connections)}, "
                    f"Total Connections: {self.connection_count}"
                )
                await asyncio.sleep(10)  # Log every 10 seconds
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in connection monitor: {e}")
                await asyncio.sleep(10)

def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    logger.info(f"Received signal {signum}, shutting down gracefully...")
    sys.exit(0)

def main():
    """Main function to run the debug server"""
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Configure uvicorn with debug-friendly settings
    config = Config(
        app="application:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        reload_dirs=["./"],
        log_level="debug",
        access_log=True,
        # Connection settings optimized for debugging
        limit_concurrency=50,  # Lower limit for easier debugging
        timeout_keep_alive=30,
        timeout_graceful_shutdown=30,
        # Enable detailed logging
        use_colors=True,
    )
    
    # Create and run the debug server
    server = DebugServer(config)
    
    logger.info("Starting AICA Agent Debug Server...")
    logger.info("Configuration:")
    logger.info(f"  - Host: {config.host}:{config.port}")
    logger.info(f"  - Reload: {config.reload}")
    logger.info(f"  - Concurrency Limit: {config.limit_concurrency}")
    logger.info(f"  - Keep-Alive Timeout: {config.timeout_keep_alive}s")
    
    try:
        server.run()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
